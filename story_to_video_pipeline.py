#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文故事转英文朗读视频 - 完整流水线脚本
一键完成从中文文本到视频制作脚本的全流程
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime
from pathlib import Path

# 导入我们的模块
try:
    from translate_story_optimized import OptimizedStoryTranslator
    from sentence_splitter import SentenceSplitter
    from video_production_script import VideoProductionScript
except ImportError as e:
    print(f"错误：无法导入必要模块 - {e}")
    print("请确保所有脚本文件都在同一目录下")
    sys.exit(1)

class StoryToVideoPipeline:
    def __init__(self, config=None):
        self.config = config or {}
        self.project_name = self.config.get('project_name', 'story_project')
        self.output_dir = self.config.get('output_dir', 'output')
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建输出目录
        self.output_path = Path(self.output_dir) / f"{self.project_name}_{self.timestamp}"
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化各个组件
        self.translator = OptimizedStoryTranslator()
        self.splitter = SentenceSplitter()
        self.video_producer = VideoProductionScript()
        
        # 文件路径
        self.files = {
            'input_story': None,
            'translated_json': self.output_path / 'translated_story.json',
            'english_text': self.output_path / 'english_story_complete.txt',
            'reading_script': self.output_path / 'reading_script.json',
            'video_script': self.output_path / 'video_production_script.json',
            'video_guide': self.output_path / 'video_production_guide.md',
            'project_report': self.output_path / 'project_report.md'
        }
        
        # 统计信息
        self.stats = {
            'start_time': time.time(),
            'chinese_chars': 0,
            'english_words': 0,
            'sentences_count': 0,
            'estimated_duration': 0,
            'translation_method': 'unknown'
        }
    
    def log(self, message, level="INFO"):
        """日志记录"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def check_api_key(self):
        """检查API密钥配置"""
        if hasattr(self.translator, 'api_key') and self.translator.api_key:
            self.log("✅ 阿里云API密钥已配置")
            return True
        else:
            self.log("⚠️  未配置阿里云API密钥，将使用备用翻译方法", "WARNING")
            return False
    
    def validate_input(self, input_file):
        """验证输入文件"""
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        if not content:
            raise ValueError("输入文件为空")
        
        self.stats['chinese_chars'] = len(content)
        self.log(f"✅ 输入文件验证通过，共 {self.stats['chinese_chars']} 个字符")
        return content
    
    def step1_translate_story(self, input_file):
        """步骤1：翻译中文故事"""
        self.log("🔄 开始步骤1：翻译中文故事")
        
        # 验证输入
        content = self.validate_input(input_file)
        self.files['input_story'] = input_file
        
        # 检查API配置
        has_api = self.check_api_key()
        
        try:
            # 执行翻译
            result = self.translator.translate_story(
                input_file, 
                str(self.files['translated_json'])
            )
            
            # 更新统计信息
            self.stats['translation_method'] = 'qwen_api' if has_api else 'fallback'
            
            # 检查翻译结果
            if os.path.exists(self.files['english_text']):
                with open(self.files['english_text'], 'r', encoding='utf-8') as f:
                    english_content = f.read()
                self.stats['english_words'] = len(english_content.split())
                self.log(f"✅ 步骤1完成：翻译生成 {self.stats['english_words']} 个英文单词")
            else:
                raise Exception("翻译文件生成失败")
                
        except Exception as e:
            self.log(f"❌ 步骤1失败: {e}", "ERROR")
            raise
    
    def step2_split_sentences(self):
        """步骤2：拆分英文句子"""
        self.log("🔄 开始步骤2：拆分英文句子")
        
        try:
            # 执行句子拆分
            script = self.splitter.process_story_file(
                str(self.files['english_text']),
                str(self.files['reading_script'])
            )
            
            # 更新统计信息
            self.stats['sentences_count'] = script['metadata']['total_sentences']
            self.stats['estimated_duration'] = script['metadata']['estimated_total_duration']
            
            self.log(f"✅ 步骤2完成：生成 {self.stats['sentences_count']} 个朗读句子")
            self.log(f"   预估时长：{self.stats['estimated_duration']:.1f}秒 ({self.stats['estimated_duration']/60:.1f}分钟)")
            
        except Exception as e:
            self.log(f"❌ 步骤2失败: {e}", "ERROR")
            raise
    
    def step3_generate_video_script(self):
        """步骤3：生成视频制作脚本"""
        self.log("🔄 开始步骤3：生成视频制作脚本")
        
        try:
            # 执行视频脚本生成
            video_script = self.video_producer.create_video_script(
                str(self.files['reading_script']),
                str(self.files['video_script'])
            )
            
            self.log(f"✅ 步骤3完成：生成完整视频制作脚本")
            self.log(f"   视频规格：{video_script['project_info']['resolution']} @ {video_script['project_info']['frame_rate']}")
            
        except Exception as e:
            self.log(f"❌ 步骤3失败: {e}", "ERROR")
            raise
    
    def generate_project_report(self):
        """生成项目报告"""
        self.log("📊 生成项目报告")
        
        # 计算处理时间
        total_time = time.time() - self.stats['start_time']
        
        report = f"""# 中文故事转英文视频项目报告

## 项目信息
- **项目名称**: {self.project_name}
- **生成时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **处理时长**: {total_time:.1f}秒
- **输出目录**: {self.output_path}

## 处理统计
- **中文字符数**: {self.stats['chinese_chars']:,}
- **英文单词数**: {self.stats['english_words']:,}
- **朗读句子数**: {self.stats['sentences_count']}
- **预估视频时长**: {self.stats['estimated_duration']:.1f}秒 ({self.stats['estimated_duration']/60:.1f}分钟)
- **翻译方法**: {self.stats['translation_method']}

## 生成文件
- **翻译数据**: `{self.files['translated_json'].name}`
- **英文文本**: `{self.files['english_text'].name}`
- **朗读脚本**: `{self.files['reading_script'].name}`
- **视频脚本**: `{self.files['video_script'].name}`
- **制作指导**: `{self.files['video_guide'].name}`

## 使用说明

### 立即开始制作
1. **音频录制**: 使用 `{self.files['reading_script'].name}` 进行录音
2. **视频制作**: 参考 `{self.files['video_script'].name}` 进行制作
3. **制作指导**: 查看 `{self.files['video_guide'].name}` 获取详细指导

### 质量指标
- 平均句子长度: {self.stats['english_words'] / max(self.stats['sentences_count'], 1):.1f} 单词
- 每分钟句子数: {self.stats['sentences_count'] / max(self.stats['estimated_duration'] / 60, 1):.1f}
- 字符压缩比: {self.stats['english_words'] * 5 / max(self.stats['chinese_chars'], 1):.2f}

## 技术规格
- **视频分辨率**: 1920x1080
- **帧率**: 24fps
- **音频格式**: 48kHz/16bit
- **编码建议**: H.264

---
*由 Story-to-Video Pipeline 自动生成*
"""
        
        with open(self.files['project_report'], 'w', encoding='utf-8') as f:
            f.write(report)
        
        self.log(f"✅ 项目报告已保存: {self.files['project_report']}")
    
    def run_pipeline(self, input_file):
        """运行完整流水线"""
        self.log("🚀 开始中文故事转英文视频流水线")
        self.log(f"📁 输出目录: {self.output_path}")
        
        try:
            # 步骤1：翻译
            self.step1_translate_story(input_file)
            
            # 步骤2：句子拆分
            self.step2_split_sentences()
            
            # 步骤3：视频脚本生成
            self.step3_generate_video_script()
            
            # 生成项目报告
            self.generate_project_report()
            
            # 完成总结
            total_time = time.time() - self.stats['start_time']
            self.log("🎉 流水线执行完成！")
            self.log(f"⏱️  总耗时: {total_time:.1f}秒")
            self.log(f"📊 生成了 {self.stats['sentences_count']} 个朗读句子")
            self.log(f"🎬 预估视频时长: {self.stats['estimated_duration']/60:.1f}分钟")
            self.log(f"📁 所有文件已保存到: {self.output_path}")
            
            return True
            
        except Exception as e:
            self.log(f"💥 流水线执行失败: {e}", "ERROR")
            return False
    
    def list_output_files(self):
        """列出生成的文件"""
        self.log("📋 生成的文件列表:")
        for key, path in self.files.items():
            if path and os.path.exists(path):
                size = os.path.getsize(path)
                self.log(f"   ✅ {path.name} ({size:,} bytes)")
            elif path:
                self.log(f"   ❌ {path.name} (未生成)")

def main():
    parser = argparse.ArgumentParser(description="中文故事转英文朗读视频完整流水线")
    parser.add_argument("input_file", help="输入的中文故事文件路径")
    parser.add_argument("--project-name", default="story_project", help="项目名称")
    parser.add_argument("--output-dir", default="output", help="输出目录")
    parser.add_argument("--list-files", action="store_true", help="完成后列出生成的文件")
    
    args = parser.parse_args()
    
    # 配置
    config = {
        'project_name': args.project_name,
        'output_dir': args.output_dir
    }
    
    # 创建并运行流水线
    pipeline = StoryToVideoPipeline(config)
    success = pipeline.run_pipeline(args.input_file)
    
    if args.list_files:
        pipeline.list_output_files()
    
    if success:
        print(f"\n🎉 项目完成！输出目录: {pipeline.output_path}")
        sys.exit(0)
    else:
        print(f"\n💥 项目失败！请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
