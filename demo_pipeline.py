#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示版流水线 - 中文故事转英文视频
使用现有翻译结果演示完整流程
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path

def create_demo_output():
    """创建演示输出目录并复制文件"""
    
    print("🎬 中文故事转英文朗读视频 - 演示流水线")
    print("=" * 50)
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = Path(f"demo_output/story_video_{timestamp}")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📁 创建输出目录: {output_dir}")
    
    # 检查必要文件
    required_files = {
        'story.txt': '原始中文故事',
        'english_story_complete.txt': '完整英文翻译',
        'reading_script.json': '朗读脚本',
        'video_production_script.json': '视频制作脚本',
        'video_production_script_guide.md': '制作指导文档'
    }
    
    missing_files = []
    for filename, description in required_files.items():
        if not os.path.exists(filename):
            missing_files.append(f"{filename} ({description})")
    
    if missing_files:
        print("❌ 缺少以下文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ 所有必要文件检查通过")
    
    # 复制文件到输出目录
    file_mapping = {
        'story.txt': 'original_story.txt',
        'english_story_complete.txt': 'english_story_complete.txt',
        'reading_script.json': 'reading_script.json',
        'video_production_script.json': 'video_production_script.json',
        'video_production_script_guide.md': 'video_production_guide.md'
    }
    
    print("\n📋 复制文件到输出目录:")
    for src, dst in file_mapping.items():
        src_path = Path(src)
        dst_path = output_dir / dst
        shutil.copy2(src_path, dst_path)
        size = dst_path.stat().st_size
        print(f"   ✅ {dst} ({size:,} bytes)")
    
    # 读取统计信息
    stats = {}
    
    # 中文字符数
    with open('story.txt', 'r', encoding='utf-8') as f:
        chinese_content = f.read()
        stats['chinese_chars'] = len(chinese_content)
    
    # 英文单词数
    with open('english_story_complete.txt', 'r', encoding='utf-8') as f:
        english_content = f.read()
        stats['english_words'] = len(english_content.split())
    
    # 朗读句子数和时长
    with open('reading_script.json', 'r', encoding='utf-8') as f:
        reading_data = json.load(f)
        stats['sentences_count'] = reading_data['metadata']['total_sentences']
        stats['estimated_duration'] = reading_data['metadata']['estimated_total_duration']
    
    # 生成项目报告
    report_content = f"""# 中文故事转英文视频项目 - 演示报告

## 项目信息
- **生成时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **输出目录**: {output_dir}
- **项目类型**: 演示版本

## 处理统计
- **中文字符数**: {stats['chinese_chars']:,}
- **英文单词数**: {stats['english_words']:,}
- **朗读句子数**: {stats['sentences_count']}
- **预估视频时长**: {stats['estimated_duration']:.1f}秒 ({stats['estimated_duration']/60:.1f}分钟)

## 生成文件
- **原始故事**: `original_story.txt`
- **英文翻译**: `english_story_complete.txt`
- **朗读脚本**: `reading_script.json`
- **视频脚本**: `video_production_script.json`
- **制作指导**: `video_production_guide.md`

## 质量指标
- 平均句子长度: {stats['english_words'] / stats['sentences_count']:.1f} 单词
- 每分钟句子数: {stats['sentences_count'] / (stats['estimated_duration'] / 60):.1f}
- 字符压缩比: {stats['english_words'] * 5 / stats['chinese_chars']:.2f}

## 使用说明

### 🎤 音频录制
使用 `reading_script.json` 中的{stats['sentences_count']}个句子进行录音：
1. 每个句子都有预估的朗读时长
2. 句子类型标注（叙述、对话、行动等）
3. 建议的语调和语速设置

### 🎬 视频制作
参考 `video_production_script.json` 进行制作：
1. 每个场景都有详细的视觉描述
2. 摄像机角度和灯光建议
3. 专业的技术规格设置

### 📖 制作指导
查看 `video_production_guide.md` 获取：
1. 完整的制作流程指南
2. 技术规格和质量标准
3. 后期制作检查清单

## 技术规格
- **视频分辨率**: 1920x1080
- **帧率**: 24fps
- **音频格式**: 48kHz/16bit
- **风格**: 电影级惊悚片配纪录片元素

## 下一步
1. 🎤 录制专业旁白音频
2. 🎬 收集或制作视觉素材
3. ✂️ 使用专业软件进行视频编辑
4. 🔍 进行质量控制和优化
5. 📤 导出多种格式的最终版本

---
*由演示流水线自动生成*
"""
    
    # 保存项目报告
    report_path = output_dir / 'project_report.md'
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"   ✅ project_report.md ({len(report_content)} bytes)")
    
    # 显示总结
    print("\n" + "=" * 50)
    print("🎉 演示流水线执行完成！")
    print(f"📊 统计信息:")
    print(f"   - 中文字符: {stats['chinese_chars']:,}")
    print(f"   - 英文单词: {stats['english_words']:,}")
    print(f"   - 朗读句子: {stats['sentences_count']}")
    print(f"   - 视频时长: {stats['estimated_duration']/60:.1f}分钟")
    print(f"📁 输出目录: {output_dir}")
    
    print("\n🚀 下一步:")
    print("1. 查看制作指导文档")
    print("2. 使用朗读脚本录制音频")
    print("3. 参考视频脚本制作视频")
    
    return True

def main():
    """主函数"""
    try:
        success = create_demo_output()
        if success:
            print("\n✨ 演示完成！所有文件已准备就绪。")
            return 0
        else:
            print("\n💥 演示失败，请检查文件。")
            return 1
    except Exception as e:
        print(f"\n❌ 错误: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
