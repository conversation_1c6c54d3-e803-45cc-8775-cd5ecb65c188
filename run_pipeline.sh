#!/bin/bash

# 中文故事转英文朗读视频 - 一键流水线脚本

echo "🎬 中文故事转英文朗读视频 - 一键流水线"
echo "================================================"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误：未找到Python3，请先安装Python 3.7+"
    exit 1
fi

# 检查story.txt是否存在
if [ ! -f "story.txt" ]; then
    echo "❌ 错误：找不到输入文件 'story.txt'"
    echo "请确保当前目录下有 story.txt 文件"
    exit 1
fi

# 检查必要的Python脚本
missing_files=""
required_files=(
    "run_story_pipeline.py"
    "story_to_video_pipeline.py" 
    "translate_story_optimized.py"
    "sentence_splitter.py"
    "video_production_script.py"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files="$missing_files $file"
    fi
done

if [ ! -z "$missing_files" ]; then
    echo "❌ 错误：缺少必要文件:$missing_files"
    exit 1
fi

echo "✅ 所有必要文件检查通过"
echo

# 检查并安装必要的Python包
echo "📦 检查并安装必要的Python包..."
python3 -c "import requests" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装 requests..."
    pip3 install requests
fi

echo
echo "🚀 开始运行流水线..."
echo

# 运行主脚本
python3 run_story_pipeline.py

if [ $? -eq 0 ]; then
    echo
    echo "🎉 流水线执行成功！"
    echo
    echo "📁 请查看 output 目录下的生成文件"
    echo
else
    echo
    echo "💥 流水线执行失败"
    exit 1
fi
