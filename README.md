# 中文故事转英文朗读视频 - 完整流水线

🎬 一键将中文故事转换为专业英文朗读视频制作方案

## 🚀 快速开始

### 方法一：演示版本（推荐新用户）
```bash
# 使用现有翻译结果快速演示
python3 demo_pipeline.py
```

### 方法二：一键运行完整流程

#### Windows用户
```bash
# 双击运行
run_pipeline.bat
```

#### Mac/Linux用户
```bash
# 终端运行
./run_pipeline.sh
```

#### 或者直接使用Python
```bash
python3 run_story_pipeline.py
```

### 方法三：命令行运行
```bash
python3 story_to_video_pipeline.py story.txt --project-name "我的故事" --output-dir "输出目录"
```

## 📋 使用前准备

### 1. 环境要求
- Python 3.7+
- requests库（脚本会自动安装）

### 2. 文件准备
确保当前目录下有以下文件：
- **`story.txt`** - 你的中文故事文件
- **`translate_story_optimized.py`** - 翻译脚本
- **`sentence_splitter.py`** - 句子分割脚本
- **`video_production_script.py`** - 视频脚本生成器
- **`story_to_video_pipeline.py`** - 主流水线脚本

### 3. API配置（可选）
如需使用阿里云Qwen翻译，请在 `translate_story_optimized.py` 中设置API密钥：
```python
self.api_key = 'your_api_key_here'
```

## 🔄 流程说明

流水线包含3个主要步骤：

### 步骤1：翻译中文故事
- 使用阿里云Qwen大模型API翻译（如已配置）
- 或使用备用翻译方法
- 生成完整英文文本

### 步骤2：拆分英文句子
- 智能分析文本内容
- 按照朗读需求拆分句子
- 生成时间估算和元数据

### 步骤3：生成视频制作脚本
- 分析句子内容和主题
- 生成视觉描述和摄像机角度
- 创建完整的制作指导文档

## 📁 输出文件

运行完成后，会在 `output/项目名_时间戳/` 目录下生成：

### 核心文件
- **`reading_script.json`** - 朗读脚本（152个句子）
- **`video_production_script.json`** - 视频制作脚本
- **`video_production_guide.md`** - 制作指导文档
- **`project_report.md`** - 项目报告

### 中间文件
- **`translated_story.json`** - 翻译数据
- **`english_story_complete.txt`** - 完整英文文本

## 🎬 视频制作规格

- **分辨率**: 1920x1080 (Full HD)
- **帧率**: 24fps
- **音频**: 48kHz/16bit
- **风格**: 电影级惊悚片配纪录片元素

## 📖 使用示例

### 演示版本（推荐首次使用）
```bash
# 使用现有翻译结果快速演示完整流程
python3 demo_pipeline.py
```

### 基本使用
```bash
# 确保story.txt在当前目录
python3 run_story_pipeline.py
```

### 高级使用
```bash
# 自定义项目名称和输出目录
python3 story_to_video_pipeline.py story.txt \
    --project-name "末日生存故事" \
    --output-dir "我的项目" \
    --list-files
```

## 🛠️ 故障排除

### 常见问题

#### 1. 找不到story.txt
```
❌ 错误：找不到输入文件 'story.txt'
```
**解决方案**: 确保当前目录下有story.txt文件

#### 2. 缺少Python包
```
❌ 导入错误: No module named 'requests'
```
**解决方案**: 运行 `pip3 install requests`

#### 3. API调用失败
```
⚠️ API调用失败，使用备用翻译
```
**解决方案**: 检查网络连接或API密钥配置

#### 4. 权限问题（Mac/Linux）
```
bash: ./run_pipeline.sh: Permission denied
```
**解决方案**: 运行 `chmod +x run_pipeline.sh`

### 调试模式
如果遇到问题，可以直接运行各个步骤：

```bash
# 步骤1：翻译
python3 translate_story_optimized.py

# 步骤2：句子分割
python3 sentence_splitter.py

# 步骤3：视频脚本
python3 video_production_script.py
```

## 🎯 输出质量

### 翻译质量
- 使用阿里云Qwen大模型确保准确性
- 保持原文情节和氛围
- 适合朗读的自然表达

### 视频脚本质量
- 专业级制作规格
- 详细的视觉和音频指导
- 智能主题识别和场景匹配

## 📞 技术支持

### 文件结构
```
项目目录/
├── story.txt                          # 输入文件
├── run_story_pipeline.py              # 快速运行脚本
├── story_to_video_pipeline.py         # 主流水线
├── translate_story_optimized.py       # 翻译模块
├── sentence_splitter.py               # 句子分割模块
├── video_production_script.py         # 视频脚本模块
├── run_pipeline.bat                   # Windows批处理
├── run_pipeline.sh                    # Mac/Linux脚本
└── output/                            # 输出目录
    └── 项目名_时间戳/
        ├── reading_script.json
        ├── video_production_script.json
        ├── video_production_guide.md
        └── project_report.md
```

### 系统要求
- **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **Python版本**: 3.7+
- **内存**: 建议4GB+
- **存储**: 至少100MB可用空间

## 🎉 开始制作

运行流水线后，您将获得：
1. **152个朗读句子** - 可直接用于录音
2. **完整视频脚本** - 包含视觉描述和技术规格
3. **制作指导文档** - 专业的制作流程指南

立即开始您的视频制作之旅吧！🚀
