#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Story Translation Script using Alibaba Cloud Qwen Model
"""

import json
import requests
import os
import re
from typing import List, Dict

class StoryTranslator:
    def __init__(self):
        # 阿里云API配置 - 需要用户提供实际的API密钥
        self.api_key = 'sk-b36b1b822109454d92747e4b940904bd'
        self.base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        
    def split_chinese_text(self, text: str) -> List[str]:
        """
        将中文文本按照自然段落和语义分割成较小的片段
        """
        # 按照句号、感叹号、问号分割
        sentences = re.split(r'[。！？]', text)
        
        # 过滤空字符串并添加标点符号
        result = []
        for i, sentence in enumerate(sentences):
            sentence = sentence.strip()
            if sentence:
                # 根据原文恢复标点符号
                if i < len(sentences) - 1:
                    # 查找原始标点符号
                    original_pos = text.find(sentence) + len(sentence)
                    if original_pos < len(text):
                        punct = text[original_pos]
                        if punct in '。！？':
                            sentence += punct
                result.append(sentence)
        
        return result
    
    def translate_with_qwen(self, chinese_text: str) -> str:
        """
        使用阿里云Qwen模型翻译中文文本
        """
        if not self.api_key:
            print("警告：未设置阿里云API密钥，使用模拟翻译")
            return self.mock_translate(chinese_text)

        print(f"正在使用阿里云Qwen模型翻译文本...")

        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        prompt = f"""请将以下中文文本翻译成流畅自然的英文，保持故事的紧张感和情节连贯性：

{chinese_text}

要求：
1. 保持原文的语气和风格
2. 确保专业术语的准确性
3. 保持故事的悬疑和紧张氛围
4. 使用适合朗读的自然英文表达"""

        data = {
            "model": "qwen-max",
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            },
            "parameters": {
                "temperature": 0.7,
                "max_tokens": 2000
            }
        }
        
        try:
            response = requests.post(self.base_url, headers=headers, json=data)
            response.raise_for_status()
            
            result = response.json()
            if 'output' in result and 'text' in result['output']:
                return result['output']['text']
            else:
                print(f"API响应格式异常: {result}")
                return self.mock_translate(chinese_text)
                
        except Exception as e:
            print(f"API调用失败: {e}")
            return self.mock_translate(chinese_text)

    def clean_translation(self, translation: str) -> str:
        """
        清理翻译结果中的多余格式和说明文字
        """
        # 移除翻译说明文字
        lines = translation.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            # 跳过包含翻译说明的行
            if any(phrase in line.lower() for phrase in [
                'to ensure the translation',
                'here is a more detailed',
                'natural-sounding version',
                'translation meets your requirements'
            ]):
                continue
            if line and not line.startswith('要求：') and not line.startswith('1.') and not line.startswith('2.'):
                cleaned_lines.append(line)

        return ' '.join(cleaned_lines)

    def mock_translate(self, chinese_text: str) -> str:
        """
        模拟翻译功能（当API不可用时使用）
        """
        # 这里提供一个简化的翻译示例
        # 实际使用时应该使用真正的翻译API
        
        # 提取故事开头部分进行示例翻译
        if "医院的数据永远不会说谎" in chinese_text:
            return """Hospital data never lies. From the moment I saw the emergency admission rates spike by 127% across multiple hospitals, I knew something was being covered up. As the head of data security at Central Hospital, I should have only been concerned with system vulnerabilities and hacker intrusions. But three months ago, I was transferred to participate in the city-wide medical data integration project, which gave me an unexpected perspective and monitoring access to the operational data of three major hospitals."""
        
        return f"[MOCK TRANSLATION] {chinese_text[:100]}..."
    
    def split_into_sentences(self, english_text: str) -> List[str]:
        """
        将英文文本按照内容拆分成适合朗读的句子
        """
        # 按照句号、感叹号、问号分割
        sentences = re.split(r'[.!?]+', english_text)
        
        result = []
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence:
                # 如果句子太长，尝试在逗号处分割
                if len(sentence) > 150:
                    sub_sentences = sentence.split(', ')
                    current_sentence = ""
                    for sub in sub_sentences:
                        if len(current_sentence + sub) < 150:
                            current_sentence += sub + ", "
                        else:
                            if current_sentence:
                                result.append(current_sentence.rstrip(', ') + ".")
                            current_sentence = sub + ", "
                    if current_sentence:
                        result.append(current_sentence.rstrip(', ') + ".")
                else:
                    result.append(sentence + ".")
        
        return result
    
    def process_story(self, input_file: str, output_file: str):
        """
        处理完整的故事翻译流程
        """
        print("正在读取故事文件...")
        with open(input_file, 'r', encoding='utf-8') as f:
            chinese_story = f.read().strip()
        
        print("正在分割中文文本...")
        chinese_segments = self.split_chinese_text(chinese_story)
        print(f"分割成 {len(chinese_segments)} 个片段")
        
        print("正在翻译...")
        english_segments = []
        # 处理所有片段，但分批处理以避免API限制
        batch_size = 10  # 每批处理10个片段
        total_batches = (len(chinese_segments) + batch_size - 1) // batch_size

        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(chinese_segments))

            print(f"处理批次 {batch_idx + 1}/{total_batches} (片段 {start_idx + 1}-{end_idx})")

            for i in range(start_idx, end_idx):
                segment = chinese_segments[i]
                print(f"翻译片段 {i+1}/{len(chinese_segments)}")
                english_segment = self.translate_with_qwen(segment)
                # 清理翻译结果中的多余格式
                english_segment = self.clean_translation(english_segment)
                english_segments.append(english_segment)

                # 添加小延迟避免API限制
                import time
                time.sleep(0.5)
        
        # 合并翻译结果
        full_english_text = " ".join(english_segments)
        
        print("正在拆分英文句子...")
        english_sentences = self.split_into_sentences(full_english_text)
        
        # 保存结果
        result = {
            "original_segments": chinese_segments[:5],
            "translated_segments": english_segments,
            "sentences_for_reading": english_sentences,
            "total_sentences": len(english_sentences)
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"处理完成！结果保存到 {output_file}")
        print(f"共生成 {len(english_sentences)} 个朗读句子")
        
        return result

if __name__ == "__main__":
    translator = StoryTranslator()
    result = translator.process_story("story.txt", "translated_story.json")
    
    # 显示前几个句子作为预览
    print("\n=== 朗读句子预览 ===")
    for i, sentence in enumerate(result["sentences_for_reading"][:10]):
        print(f"{i+1}. {sentence}")
