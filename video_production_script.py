#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Video Production Script Generator for English Story Reading
"""

import json
from typing import List, Dict

class VideoProductionScript:
    def __init__(self):
        self.visual_themes = {
            "hospital": ["hospital corridor", "medical equipment", "computer screens", "data charts"],
            "data": ["computer screens", "data visualization", "charts and graphs", "code on screen"],
            "tension": ["dark atmosphere", "red warning lights", "close-up shots", "dramatic shadows"],
            "surveillance": ["security cameras", "monitoring screens", "CCTV footage", "control room"],
            "emergency": ["ambulance lights", "emergency vehicles", "medical staff running", "urgent atmosphere"],
            "mystery": ["dark corridors", "mysterious figures", "silhouettes", "fog/smoke effects"],
            "preparation": ["shopping scenes", "packing supplies", "home fortification", "survival gear"],
            "apocalypse": ["empty streets", "abandoned vehicles", "broken windows", "chaos scenes"]
        }
        
        self.audio_settings = {
            "narrative": {"pace": "normal", "tone": "serious", "volume": "medium"},
            "dialogue": {"pace": "natural", "tone": "conversational", "volume": "medium"},
            "action": {"pace": "fast", "tone": "urgent", "volume": "high"},
            "internal": {"pace": "slow", "tone": "thoughtful", "volume": "low"},
            "tension": {"pace": "slow", "tone": "ominous", "volume": "medium"}
        }
    
    def analyze_sentence_content(self, sentence: str) -> List[str]:
        """
        分析句子内容，确定适合的视觉主题
        """
        sentence_lower = sentence.lower()
        themes = []
        
        # 医院相关
        if any(word in sentence_lower for word in ['hospital', 'medical', 'emergency', 'patient', 'doctor', 'nurse']):
            themes.append("hospital")
        
        # 数据相关
        if any(word in sentence_lower for word in ['data', 'computer', 'system', 'screen', 'records', 'database']):
            themes.append("data")
        
        # 监控相关
        if any(word in sentence_lower for word in ['surveillance', 'camera', 'monitoring', 'footage', 'security']):
            themes.append("surveillance")
        
        # 紧急情况
        if any(word in sentence_lower for word in ['emergency', 'urgent', 'crisis', 'alarm', 'alert']):
            themes.append("emergency")
        
        # 神秘/悬疑
        if any(word in sentence_lower for word in ['mysterious', 'strange', 'suspicious', 'hidden', 'secret']):
            themes.append("mystery")
        
        # 准备/生存
        if any(word in sentence_lower for word in ['supplies', 'food', 'water', 'prepare', 'survival']):
            themes.append("preparation")
        
        # 末日场景
        if any(word in sentence_lower for word in ['infected', 'virus', 'chaos', 'collapse', 'abandoned']):
            themes.append("apocalypse")
        
        # 紧张氛围
        if any(word in sentence_lower for word in ['suddenly', 'shocked', 'terrified', 'dangerous', 'fear']):
            themes.append("tension")
        
        return themes if themes else ["hospital"]  # 默认主题
    
    def generate_visual_description(self, sentence: str, themes: List[str]) -> str:
        """
        根据主题生成视觉描述
        """
        primary_theme = themes[0]
        visual_elements = self.visual_themes.get(primary_theme, ["generic scene"])
        
        # 根据句子内容选择具体的视觉元素
        if "data" in sentence.lower():
            return "Close-up of computer screen showing medical data charts and graphs with increasing red warning indicators"
        elif "hospital" in sentence.lower():
            return "Wide shot of hospital corridor with medical staff moving urgently in the background"
        elif "surveillance" in sentence.lower() or "camera" in sentence.lower():
            return "Security monitoring room with multiple screens showing hospital surveillance footage"
        elif "emergency" in sentence.lower():
            return "Emergency department with flashing lights and medical equipment, creating urgent atmosphere"
        elif "virus" in sentence.lower() or "infected" in sentence.lower():
            return "Dark, ominous scene with biohazard symbols and protective equipment"
        else:
            return f"Scene depicting {primary_theme} with dramatic lighting and professional cinematography"
    
    def create_video_script(self, reading_script_file: str, output_file: str):
        """
        创建完整的视频制作脚本
        """
        print(f"正在读取朗读脚本: {reading_script_file}")
        with open(reading_script_file, 'r', encoding='utf-8') as f:
            reading_script = json.load(f)
        
        print("正在生成视频制作脚本...")
        
        video_script = {
            "project_info": {
                "title": reading_script["metadata"]["title"],
                "duration": reading_script["metadata"]["estimated_total_duration"],
                "total_scenes": reading_script["metadata"]["total_sentences"],
                "genre": reading_script["metadata"]["genre"],
                "resolution": "1920x1080",
                "frame_rate": "24fps",
                "audio_format": "48kHz/16bit"
            },
            "production_notes": {
                "style": "Cinematic thriller with documentary elements",
                "color_palette": "Dark blues, grays, and red accents for tension",
                "lighting": "Dramatic, high contrast lighting",
                "camera_work": "Mix of static shots and subtle movements",
                "text_overlay": "Clean, modern font for subtitles"
            },
            "scenes": []
        }
        
        for sentence_data in reading_script["sentences"]:
            themes = self.analyze_sentence_content(sentence_data["text"])
            visual_desc = self.generate_visual_description(sentence_data["text"], themes)
            audio_setting = self.audio_settings.get(sentence_data["type"], self.audio_settings["narrative"])
            
            scene = {
                "scene_id": sentence_data["id"],
                "start_time": sentence_data["start_time"],
                "end_time": sentence_data["end_time"],
                "duration": sentence_data["estimated_duration"],
                "text": sentence_data["text"],
                "audio": {
                    "narration": sentence_data["text"],
                    "pace": audio_setting["pace"],
                    "tone": audio_setting["tone"],
                    "volume": audio_setting["volume"],
                    "voice_type": "Professional male narrator with clear diction"
                },
                "visual": {
                    "description": visual_desc,
                    "themes": themes,
                    "camera_angle": self.suggest_camera_angle(sentence_data["text"]),
                    "lighting": self.suggest_lighting(themes),
                    "effects": self.suggest_effects(sentence_data["type"])
                },
                "subtitles": {
                    "text": sentence_data["text"],
                    "position": "bottom_center",
                    "font": "Arial Bold",
                    "size": "24pt",
                    "color": "white",
                    "background": "semi-transparent black"
                }
            }
            
            video_script["scenes"].append(scene)
        
        # 保存视频脚本
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(video_script, f, ensure_ascii=False, indent=2)
        
        print(f"视频制作脚本已保存到: {output_file}")
        
        # 生成制作指导
        self.generate_production_guide(video_script, output_file.replace('.json', '_guide.md'))
        
        return video_script
    
    def suggest_camera_angle(self, text: str) -> str:
        """
        根据文本内容建议摄像机角度
        """
        text_lower = text.lower()
        if any(word in text_lower for word in ['shocked', 'stunned', 'realized']):
            return "Close-up on face/eyes"
        elif any(word in text_lower for word in ['data', 'screen', 'computer']):
            return "Over-shoulder shot of computer screen"
        elif any(word in text_lower for word in ['hospital', 'corridor', 'building']):
            return "Wide establishing shot"
        elif any(word in text_lower for word in ['surveillance', 'monitoring']):
            return "Security camera POV"
        else:
            return "Medium shot"
    
    def suggest_lighting(self, themes: List[str]) -> str:
        """
        根据主题建议灯光设置
        """
        if "tension" in themes or "mystery" in themes:
            return "Low-key lighting with dramatic shadows"
        elif "hospital" in themes:
            return "Bright, clinical fluorescent lighting"
        elif "data" in themes:
            return "Blue-tinted computer screen glow"
        elif "apocalypse" in themes:
            return "Harsh, emergency lighting with red accents"
        else:
            return "Natural, balanced lighting"
    
    def suggest_effects(self, sentence_type: str) -> List[str]:
        """
        根据句子类型建议视觉效果
        """
        effects = []
        if sentence_type == "action":
            effects.extend(["Quick cuts", "Slight camera shake"])
        elif sentence_type == "tension":
            effects.extend(["Slow zoom", "Color desaturation"])
        elif sentence_type == "dialogue":
            effects.extend(["Subtle focus pull", "Natural movement"])
        elif sentence_type == "internal":
            effects.extend(["Soft focus", "Gentle fade"])
        
        return effects
    
    def generate_production_guide(self, video_script: Dict, output_file: str):
        """
        生成制作指导文档
        """
        guide_content = f"""# Video Production Guide: {video_script['project_info']['title']}

## Project Overview
- **Duration**: {video_script['project_info']['duration']:.1f} seconds ({video_script['project_info']['duration']/60:.1f} minutes)
- **Total Scenes**: {video_script['project_info']['total_scenes']}
- **Genre**: {video_script['project_info']['genre']}
- **Resolution**: {video_script['project_info']['resolution']}
- **Frame Rate**: {video_script['project_info']['frame_rate']}

## Production Style
{video_script['production_notes']['style']}

### Visual Style
- **Color Palette**: {video_script['production_notes']['color_palette']}
- **Lighting**: {video_script['production_notes']['lighting']}
- **Camera Work**: {video_script['production_notes']['camera_work']}

## Audio Requirements
- Professional male narrator with clear, authoritative voice
- Background music: Subtle, tension-building instrumental
- Sound effects: Hospital ambience, computer sounds, emergency alerts

## Scene Breakdown (First 10 scenes)
"""
        
        for i, scene in enumerate(video_script['scenes'][:10]):
            guide_content += f"""
### Scene {scene['scene_id']} ({scene['start_time']:.1f}s - {scene['end_time']:.1f}s)
**Text**: "{scene['text']}"
**Visual**: {scene['visual']['description']}
**Camera**: {scene['visual']['camera_angle']}
**Lighting**: {scene['visual']['lighting']}
**Audio**: {scene['audio']['pace']} pace, {scene['audio']['tone']} tone
"""
        
        guide_content += f"\n... and {len(video_script['scenes']) - 10} more scenes\n"
        
        guide_content += """
## Technical Specifications
- Use professional video editing software (Adobe Premiere Pro, Final Cut Pro, or DaVinci Resolve)
- Export in H.264 format for web distribution
- Include closed captions for accessibility
- Maintain consistent audio levels throughout

## Post-Production Checklist
- [ ] Color correction and grading
- [ ] Audio mixing and mastering
- [ ] Subtitle synchronization
- [ ] Final quality review
- [ ] Export in multiple formats (web, mobile, high-quality)
"""
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"制作指导文档已保存到: {output_file}")

if __name__ == "__main__":
    producer = VideoProductionScript()
    script = producer.create_video_script("reading_script.json", "video_production_script.json")
