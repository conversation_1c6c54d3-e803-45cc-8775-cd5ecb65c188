#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版本的中文故事翻译脚本
将文本合并成更大的块来减少API调用次数
"""

import json
import re
import requests
import time
from typing import List

class OptimizedStoryTranslator:
    def __init__(self):
        # 阿里云API配置
        self.api_key = 'sk-b36b1b822109454d92747e4b940904bd'
        self.base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        
    def split_story_into_chunks(self, text: str, max_chunk_size: int = 1500) -> List[str]:
        """
        将故事分割成适合翻译的大块
        """
        # 按段落分割
        paragraphs = text.split('\n\n')
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
                
            # 如果当前块加上新段落不会超过限制，就添加
            if len(current_chunk + paragraph) < max_chunk_size:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
            else:
                # 保存当前块，开始新块
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = paragraph
        
        # 添加最后一块
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    
    def translate_chunk_with_qwen(self, chinese_text: str) -> str:
        """
        使用阿里云Qwen模型翻译大块文本
        """
        if not self.api_key:
            print("警告：未设置阿里云API密钥")
            return chinese_text
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        prompt = f"""请将以下中文故事文本翻译成流畅自然的英文，保持故事的紧张感和情节连贯性。请直接返回翻译结果，不要添加任何说明文字：

{chinese_text}

翻译要求：
1. 保持原文的语气和风格
2. 确保专业术语的准确性
3. 保持故事的悬疑和紧张氛围
4. 使用适合朗读的自然英文表达
5. 保持段落结构"""

        data = {
            "model": "qwen-max",
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            },
            "parameters": {
                "temperature": 0.7,
                "max_tokens": 3000
            }
        }
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                print(f"正在翻译文本块 (尝试 {attempt + 1}/{max_retries})...")
                response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
                response.raise_for_status()
                
                result = response.json()
                if 'output' in result and 'text' in result['output']:
                    translation = result['output']['text'].strip()
                    # 清理翻译结果
                    translation = self.clean_translation(translation)
                    return translation
                else:
                    print(f"API响应格式异常: {result}")
                    
            except Exception as e:
                print(f"API调用失败 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    print("等待5秒后重试...")
                    time.sleep(5)
                else:
                    print("所有重试都失败，使用原文")
                    return chinese_text
        
        return chinese_text
    
    def clean_translation(self, translation: str) -> str:
        """
        清理翻译结果中的多余格式和说明文字
        """
        # 移除常见的翻译说明文字
        lines = translation.split('\n')
        cleaned_lines = []
        
        skip_phrases = [
            'to ensure the translation',
            'here is a more detailed',
            'natural-sounding version',
            'translation meets your requirements',
            'here is the translation',
            'the translation is',
            'translated version',
            'english translation'
        ]
        
        for line in lines:
            line = line.strip()
            # 跳过包含翻译说明的行
            if any(phrase in line.lower() for phrase in skip_phrases):
                continue
            # 跳过要求列表
            if line.startswith(('要求：', '1.', '2.', '3.', '4.', '5.')):
                continue
            if line:
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def split_into_sentences(self, text: str) -> List[str]:
        """
        将英文文本拆分成适合朗读的句子
        """
        # 按句号、感叹号、问号分割
        sentences = re.split(r'[.!?]+', text)
        
        result = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 20:  # 只保留有意义的句子
                # 如果句子太长，在逗号处进一步分割
                if len(sentence) > 120:
                    sub_sentences = sentence.split(', ')
                    current = ""
                    for sub in sub_sentences:
                        if len(current + sub) < 120:
                            current += sub + ", " if current else sub
                        else:
                            if current:
                                result.append(current.rstrip(', ') + ".")
                            current = sub
                    if current:
                        result.append(current.rstrip(', ') + ".")
                else:
                    result.append(sentence + ".")
        
        return result
    
    def translate_story(self, input_file: str, output_file: str):
        """
        翻译完整故事
        """
        print(f"正在读取故事文件: {input_file}")
        with open(input_file, 'r', encoding='utf-8') as f:
            chinese_story = f.read().strip()
        
        print("正在分割故事为翻译块...")
        chunks = self.split_story_into_chunks(chinese_story)
        print(f"分割成 {len(chunks)} 个翻译块")
        
        print("开始翻译...")
        english_chunks = []
        
        for i, chunk in enumerate(chunks):
            print(f"\n=== 翻译块 {i+1}/{len(chunks)} ===")
            print(f"块大小: {len(chunk)} 字符")
            
            english_chunk = self.translate_chunk_with_qwen(chunk)
            english_chunks.append(english_chunk)
            
            # 添加延迟避免API限制
            if i < len(chunks) - 1:
                print("等待2秒...")
                time.sleep(2)
        
        # 合并翻译结果
        full_english_text = '\n\n'.join(english_chunks)
        
        # 保存完整英文文本
        english_file = output_file.replace('.json', '_complete.txt')
        with open(english_file, 'w', encoding='utf-8') as f:
            f.write(full_english_text)
        print(f"完整英文文本已保存到: {english_file}")
        
        # 拆分成句子
        print("正在拆分成朗读句子...")
        sentences = self.split_into_sentences(full_english_text)
        
        # 保存结果
        result = {
            "original_chunks": chunks,
            "translated_chunks": english_chunks,
            "full_english_text": full_english_text,
            "sentences_for_reading": sentences,
            "total_sentences": len(sentences),
            "total_chunks": len(chunks)
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n=== 翻译完成 ===")
        print(f"翻译块数: {len(chunks)}")
        print(f"朗读句子数: {len(sentences)}")
        print(f"结果保存到: {output_file}")
        
        # 显示前10个句子预览
        print(f"\n=== 前10个朗读句子预览 ===")
        for i, sentence in enumerate(sentences[:10]):
            print(f"{i+1:2d}. {sentence}")
        
        if len(sentences) > 10:
            print(f"... 还有 {len(sentences) - 10} 个句子")
        
        return result

if __name__ == "__main__":
    translator = OptimizedStoryTranslator()
    result = translator.translate_story("story.txt", "translated_story_optimized.json")
