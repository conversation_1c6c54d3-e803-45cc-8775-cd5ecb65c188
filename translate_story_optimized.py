#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版本的中文故事翻译脚本
将文本合并成更大的块来减少API调用次数
"""

import json
import re
import requests
import time
from typing import List

class OptimizedStoryTranslator:
    def __init__(self):
        # 阿里云API配置
        self.api_key = 'sk-1bc2f11070aa4217a660670dc6f90365'
        self.base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

        # 翻译配置 - 根据文档优化
        self.max_chunk_size = 800  # 减小块大小避免超时
        self.retry_times = 3  # 重试次数
        self.retry_delay = 5  # 重试间隔（秒）
        self.timeout = 180  # 请求超时时间（秒），根据阿里云文档建议
        
    def split_story_into_chunks(self, text: str, max_chunk_size: int = 800) -> List[str]:
        """
        将故事分割成适合翻译的大块
        """
        # 如果文本本身就很短，直接返回
        if len(text) <= max_chunk_size:
            return [text]

        chunks = []

        # 首先尝试按段落分割
        paragraphs = text.split('\n\n')
        current_chunk = ""

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # 如果单个段落就超过限制，需要进一步分割
            if len(paragraph) > max_chunk_size:
                # 保存当前块
                if current_chunk:
                    chunks.append(current_chunk)
                    current_chunk = ""

                # 按句子分割长段落
                sentences = re.split(r'[。！？.!?]+', paragraph)
                temp_chunk = ""

                for sentence in sentences:
                    sentence = sentence.strip()
                    if not sentence:
                        continue

                    if len(temp_chunk + sentence) < max_chunk_size:
                        temp_chunk += sentence + "。" if temp_chunk else sentence
                    else:
                        if temp_chunk:
                            chunks.append(temp_chunk)
                        temp_chunk = sentence

                if temp_chunk:
                    current_chunk = temp_chunk
            else:
                # 检查是否可以添加到当前块
                if len(current_chunk + "\n\n" + paragraph) < max_chunk_size:
                    if current_chunk:
                        current_chunk += "\n\n" + paragraph
                    else:
                        current_chunk = paragraph
                else:
                    # 保存当前块，开始新块
                    if current_chunk:
                        chunks.append(current_chunk)
                    current_chunk = paragraph

        # 添加最后一块
        if current_chunk:
            chunks.append(current_chunk)

        # 确保没有空块
        chunks = [chunk for chunk in chunks if chunk.strip()]

        return chunks
    
    def translate_chunk_with_qwen(self, chinese_text: str) -> str:
        """
        使用阿里云Qwen模型翻译大块文本
        """
        if not self.api_key:
            print("警告：未设置阿里云API密钥")
            return chinese_text
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        prompt = f"""请将以下中文故事文本翻译成流畅自然的英文，保持故事的紧张感和情节连贯性。请直接返回翻译结果，不要添加任何说明文字：

{chinese_text}

翻译要求：
1. 保持原文的语气和风格
2. 确保专业术语的准确性
3. 保持故事的悬疑和紧张氛围
4. 使用适合朗读的自然英文表达
5. 保持段落结构"""

        data = {
            "model": "qwen-plus",  # 使用qwen-plus，速度更快，成本更低
            "input": {
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的中英文翻译专家，擅长翻译文学作品。请直接输出翻译结果，不要添加任何解释或说明。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            },
            "parameters": {
                "temperature": 0.3,  # 降低温度，提高翻译一致性
                "max_tokens": 2000,  # 减少token数量，避免超时
                "result_format": "message"  # 使用message格式
            }
        }
        
        for attempt in range(self.retry_times):
            try:
                print(f"正在翻译文本块 (尝试 {attempt + 1}/{self.retry_times})...")
                response = requests.post(
                    self.base_url,
                    headers=headers,
                    json=data,
                    timeout=self.timeout
                )
                response.raise_for_status()

                result = response.json()

                # 检查是否有超时标识
                if response.headers.get('x-dashscope-partialresponse') == 'true':
                    print("⚠️ 检测到部分响应（可能超时），但仍尝试使用返回内容")

                # 处理新的响应格式
                if 'output' in result and 'choices' in result['output']:
                    choices = result['output']['choices']
                    if choices and len(choices) > 0:
                        message = choices[0].get('message', {})
                        translation = message.get('content', '').strip()
                        if translation:
                            # 清理翻译结果
                            translation = self.clean_translation(translation)
                            return translation

                # 兼容旧格式
                elif 'output' in result and 'text' in result['output']:
                    translation = result['output']['text'].strip()
                    translation = self.clean_translation(translation)
                    return translation

                print(f"API响应格式异常: {result}")

            except requests.exceptions.Timeout:
                print(f"请求超时 (尝试 {attempt + 1}): 超过{self.timeout}秒")
                if attempt < self.retry_times - 1:
                    print(f"等待{self.retry_delay}秒后重试...")
                    time.sleep(self.retry_delay)
            except Exception as e:
                print(f"API调用失败 (尝试 {attempt + 1}): {e}")
                if attempt < self.retry_times - 1:
                    print(f"等待{self.retry_delay}秒后重试...")
                    time.sleep(self.retry_delay)
                else:
                    print("所有重试都失败，使用原文")
                    return chinese_text

        return chinese_text
    
    def clean_translation(self, translation: str) -> str:
        """
        清理翻译结果中的多余格式和说明文字
        """
        # 移除常见的翻译说明文字
        lines = translation.split('\n')
        cleaned_lines = []
        
        skip_phrases = [
            'to ensure the translation',
            'here is a more detailed',
            'natural-sounding version',
            'translation meets your requirements',
            'here is the translation',
            'the translation is',
            'translated version',
            'english translation'
        ]
        
        for line in lines:
            line = line.strip()
            # 跳过包含翻译说明的行
            if any(phrase in line.lower() for phrase in skip_phrases):
                continue
            # 跳过要求列表
            if line.startswith(('要求：', '1.', '2.', '3.', '4.', '5.')):
                continue
            if line:
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def split_into_sentences(self, text: str) -> List[str]:
        """
        将英文文本拆分成适合朗读的句子
        """
        # 按句号、感叹号、问号分割
        sentences = re.split(r'[.!?]+', text)
        
        result = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 20:  # 只保留有意义的句子
                # 如果句子太长，在逗号处进一步分割
                if len(sentence) > 120:
                    sub_sentences = sentence.split(', ')
                    current = ""
                    for sub in sub_sentences:
                        if len(current + sub) < 120:
                            current += sub + ", " if current else sub
                        else:
                            if current:
                                result.append(current.rstrip(', ') + ".")
                            current = sub
                    if current:
                        result.append(current.rstrip(', ') + ".")
                else:
                    result.append(sentence + ".")
        
        return result
    
    def translate_story(self, input_file: str, output_file: str):
        """
        翻译完整故事
        """
        print(f"正在读取故事文件: {input_file}")
        with open(input_file, 'r', encoding='utf-8') as f:
            chinese_story = f.read().strip()
        
        print("正在分割故事为翻译块...")
        chunks = self.split_story_into_chunks(chinese_story, self.max_chunk_size)
        print(f"分割成 {len(chunks)} 个翻译块（每块最大{self.max_chunk_size}字符）")
        
        print("开始翻译...")
        english_chunks = []
        
        for i, chunk in enumerate(chunks):
            print(f"\n=== 翻译块 {i+1}/{len(chunks)} ===")
            print(f"块大小: {len(chunk)} 字符")
            
            english_chunk = self.translate_chunk_with_qwen(chunk)
            english_chunks.append(english_chunk)
            
            # 添加延迟避免API限制
            if i < len(chunks) - 1:
                print("等待2秒...")
                time.sleep(2)
        
        # 合并翻译结果
        full_english_text = '\n\n'.join(english_chunks)
        
        # 保存完整英文文本
        english_file = output_file.replace('.json', '_complete.txt')
        with open(english_file, 'w', encoding='utf-8') as f:
            f.write(full_english_text)
        print(f"完整英文文本已保存到: {english_file}")
        
        # 拆分成句子
        print("正在拆分成朗读句子...")
        sentences = self.split_into_sentences(full_english_text)
        
        # 保存结果
        result = {
            "original_chunks": chunks,
            "translated_chunks": english_chunks,
            "full_english_text": full_english_text,
            "sentences_for_reading": sentences,
            "total_sentences": len(sentences),
            "total_chunks": len(chunks)
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n=== 翻译完成 ===")
        print(f"翻译块数: {len(chunks)}")
        print(f"朗读句子数: {len(sentences)}")
        print(f"结果保存到: {output_file}")
        
        # 显示前10个句子预览
        print(f"\n=== 前10个朗读句子预览 ===")
        for i, sentence in enumerate(sentences[:10]):
            print(f"{i+1:2d}. {sentence}")
        
        if len(sentences) > 10:
            print(f"... 还有 {len(sentences) - 10} 个句子")
        
        return result

if __name__ == "__main__":
    translator = OptimizedStoryTranslator()
    result = translator.translate_story("story.txt", "translated_story_optimized.json")
