#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的翻译脚本
"""

import os
import sys
from translate_story_optimized import OptimizedStoryTranslator

def test_small_chunk():
    """测试小块翻译"""
    print("🧪 测试优化后的翻译脚本")
    print("=" * 50)
    
    # 检查输入文件
    if not os.path.exists("story.txt"):
        print("❌ 找不到 story.txt 文件")
        return False
    
    # 读取原文件大小
    with open("story.txt", 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"📖 原文件大小: {len(content)} 字符")
    
    # 创建翻译器
    translator = OptimizedStoryTranslator()
    print(f"⚙️ 配置信息:")
    print(f"   - 最大块大小: {translator.max_chunk_size} 字符")
    print(f"   - 超时时间: {translator.timeout} 秒")
    print(f"   - 重试次数: {translator.retry_times}")
    print(f"   - 使用模型: qwen-plus")
    
    # 测试分块
    chunks = translator.split_story_into_chunks(content, translator.max_chunk_size)
    print(f"📊 分块结果: {len(chunks)} 个块")
    
    for i, chunk in enumerate(chunks):
        print(f"   块 {i+1}: {len(chunk)} 字符")
    
    # 询问是否继续翻译
    print("\n🤔 是否继续进行实际翻译测试？")
    print("注意：这将调用阿里云API，可能产生费用")
    choice = input("输入 'y' 继续，其他键取消: ").strip().lower()
    
    if choice != 'y':
        print("✋ 测试取消")
        return True
    
    print("\n🚀 开始翻译测试...")
    
    try:
        # 执行翻译
        result = translator.translate_story("story.txt", "test_translation_result.json")
        
        print("\n✅ 翻译测试完成！")
        print(f"📊 结果统计:")
        print(f"   - 翻译块数: {result['total_chunks']}")
        print(f"   - 朗读句子数: {result['total_sentences']}")
        
        # 检查生成的文件
        files_to_check = [
            "test_translation_result.json",
            "test_translation_result_complete.txt"
        ]
        
        print(f"\n📁 生成的文件:")
        for filename in files_to_check:
            if os.path.exists(filename):
                size = os.path.getsize(filename)
                print(f"   ✅ {filename} ({size:,} bytes)")
            else:
                print(f"   ❌ {filename} (未生成)")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 翻译测试失败: {e}")
        return False

def test_api_connection():
    """测试API连接"""
    print("\n🔗 测试API连接...")
    
    translator = OptimizedStoryTranslator()
    
    # 测试一个小文本
    test_text = "这是一个测试。"
    
    try:
        result = translator.translate_chunk_with_qwen(test_text)
        if result and result != test_text:
            print("✅ API连接正常，翻译功能可用")
            print(f"   测试翻译: '{test_text}' -> '{result}'")
            return True
        else:
            print("⚠️ API连接可能有问题，返回了原文")
            return False
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 阿里云翻译脚本优化测试")
    print("=" * 50)
    
    # 测试API连接
    if not test_api_connection():
        print("\n💡 建议检查:")
        print("1. API密钥是否正确")
        print("2. 网络连接是否正常")
        print("3. 阿里云账户余额是否充足")
        return False
    
    # 测试完整翻译流程
    success = test_small_chunk()
    
    if success:
        print("\n🎉 所有测试通过！")
        print("\n📋 优化要点:")
        print("1. ✅ 减小块大小到800字符，避免超时")
        print("2. ✅ 使用qwen-plus模型，速度更快")
        print("3. ✅ 添加系统提示，提高翻译质量")
        print("4. ✅ 改进错误处理和重试机制")
        print("5. ✅ 支持部分响应处理")
        
        print("\n🚀 现在可以使用优化后的脚本:")
        print("python3 translate_story_optimized.py")
        print("或者运行完整流水线:")
        print("python3 demo_pipeline.py")
    else:
        print("\n💥 测试失败，请检查配置")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
