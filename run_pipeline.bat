@echo off
chcp 65001 >nul
echo 🎬 中文故事转英文朗读视频 - 一键流水线
echo ================================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 检查story.txt是否存在
if not exist "story.txt" (
    echo ❌ 错误：找不到输入文件 'story.txt'
    echo 请确保当前目录下有 story.txt 文件
    pause
    exit /b 1
)

REM 检查必要的Python脚本
set "missing_files="
if not exist "run_story_pipeline.py" set "missing_files=%missing_files% run_story_pipeline.py"
if not exist "story_to_video_pipeline.py" set "missing_files=%missing_files% story_to_video_pipeline.py"
if not exist "translate_story_optimized.py" set "missing_files=%missing_files% translate_story_optimized.py"
if not exist "sentence_splitter.py" set "missing_files=%missing_files% sentence_splitter.py"
if not exist "video_production_script.py" set "missing_files=%missing_files% video_production_script.py"

if not "%missing_files%"=="" (
    echo ❌ 错误：缺少必要文件:%missing_files%
    pause
    exit /b 1
)

echo ✅ 所有必要文件检查通过
echo.

REM 安装必要的Python包
echo 📦 检查并安装必要的Python包...
python -c "import requests" 2>nul
if errorlevel 1 (
    echo 正在安装 requests...
    pip install requests
)

echo.
echo 🚀 开始运行流水线...
echo.

REM 运行主脚本
python run_story_pipeline.py

if errorlevel 1 (
    echo.
    echo 💥 流水线执行失败
    pause
    exit /b 1
) else (
    echo.
    echo 🎉 流水线执行成功！
    echo.
    echo 📁 请查看 output 目录下的生成文件
    echo.
)

pause
