Hospital data never lies. From the moment I saw the emergency admission rates spike by 127% across multiple hospitals, I knew something was being covered up. As the head of data security at Central Hospital, I should have only been concerned with system vulnerabilities and hacker intrusions. But three months ago, I was transferred to participate in the city-wide medical data integration project, which gave me an unexpected perspective and monitoring access to the operational data of three major hospitals.

That Monday morning, I arrived at work at six as usual, made a cup of coffee, and began checking the weekend system logs. It started as routine inspection until I noticed unusually high data access volumes in the emergency department. Out of professional instinct, I clicked on the detailed records. The data on the screen left me stunned.

Emergency admission rates compared to the same period last week: First Hospital increased by 186%, Central Hospital by 217%, East District Hospital by 193%. This level of growth only had two possible explanations: system failure or major disaster. I immediately cross-checked other data sources and ruled out system failure. Something was wrong.

I put down my coffee cup and typed rapidly on the keyboard. If a major disaster had really occurred, the news would have been everywhere. But I had scrolled through my phone for two hours last night and saw no related reports. The entire city appeared calm on the surface, yet the hospital data was screaming abnormalities.

"Morning, Director <PERSON>," my supervisor pushed the door open. "I heard your wife is returning from abroad today. Planning to take leave to pick her up?" "Three o'clock flight this afternoon. I'll try to leave early," I replied with a smile, discreetly switching screens. Director <PERSON> nodded, "Alright, you've been working overtime enough lately." He paused, "By the way, there's a security drill at the hospital today. You might see some unfamiliar personnel. <PERSON>'t worry, it's just a security drill."

I frowned slightly. "How come I didn't receive any notification?" "Last-minute decision from above," she replied casually, but her eyes seemed evasive. "Mainly the emergency department. Just focus on your work." After Director <PERSON> left, I immediately pulled up the staff schedules for the three hospitals. As expected, the medical staff attendance records had been tampered with. The actual clock-in data showed emergency department staff absence rates as high as 60%, while the system schedules showed normal staffing.

Someone was covering up the truth. I took a deep breath and decided to risk going further. The hospital's electronic medical record system had strict access permissions, but the data integration project gave me a backdoor. I could view aggregated data under the guise of system maintenance. The diagnostic records left me even more confused.

A large number of patients' symptoms had been modified. Originally recorded fever, headache, and eye congestion were uniformly changed to flu-like symptoms, but the medications used didn't match flu treatment protocols at all. Even more bizarre, the follow-up records for these patients had almost entirely disappeared. They seemed to have vanished into thin air after admission.

My heart rate accelerated, cold sweat beading on my forehead. This wasn't ordinary data error or system failure, but organized, planned information control. A message popped up in the bottom right corner of my computer - a keyword monitoring alert I had set up. The hospital's internal email system showed abnormally high frequency use of "isolation" related terms.

I clicked the link and found numerous emails discussing the establishment and management of special isolation areas. These emails all had clear senders and recipients, but the content was encrypted. According to regulations, internal hospital emails shouldn't contain encrypted content unless involving special circumstances.

"Hey Chen, are you busy?" Little Wang, a technician from my team, poked his head in. "Dr. Li from the emergency department says the monitoring system is having problems. Can you help take a look?" "What kind of problems?" I asked, my heart stirring. "Says several cameras keep freezing, and there was complete blackout for a period." "I'll go over now." This was exactly the opportunity I needed.

As the data security supervisor, I had access to the monitoring system's backend. If there were any abnormal situations, the surveillance footage wouldn't lie. In the monitoring room, only one duty officer was present. After I showed my ID, he quickly gave up his position.

"Which cameras are having problems?" I asked. "Cameras 4 and 6 in Emergency Area C, and camera 9 at the back door," the duty officer pointed at the screen. "Between 2 and 4 AM last night, the footage was intermittent."

I opened last night's video playback. The Emergency Area C footage was mostly normal until 2:47 AM when a patient suddenly convulsed violently, knocking over an IV stand. Two nurses approached to check, and one was scratched on the arm by the patient. In the next dozen seconds, the image began shaking, then went completely black. When the footage resumed, the patient was gone, leaving only several medical staff cleaning the floor.

I immediately pulled up the back door surveillance for the same time period. At the hospital's back door, several people in protective suits carried a restrained stretcher to an unmarked vehicle. A figure on the stretcher was vaguely visible, struggling violently. My heartbeat thundered in my ears.

I forced myself to calm down and continued searching for other similar incidents. The results were disturbing. In the past 48 hours, the three hospitals had experienced thirteen similar agitation incidents, each followed by the appearance of mysterious personnel. All these incidents were officially recorded as intoxication or mental abnormalities. Looking back a week earlier, such incidents were almost zero.

In the final surveillance footage, I noticed a detail: the protective suit personnel wore not the hospital's standard ID badges, but military temporary passes. My fingers trembled on the keyboard. Military involvement, data tampering, patient disappearances - all clues pointed to a terrifying possibility: some highly dangerous infectious disease was quietly spreading, and officials were trying to control information.

The phone suddenly rang. It was my wife, Lin Xue, an architectural designer who had just finished a project in Germany and returned home. "Honey, I just got off the plane," her voice sounded tired. "The airport is strange. Security is much stricter than usual, and there are people in protective suits screening passengers for fever."

My heart sank. "I heard there's flu going around recently. Probably enhanced prevention measures," I tried to keep my voice calm. "Are you going straight home or to the office?" "I'll go home to rest first. Oh, someone at the airport said the road near the Military Medical Research Institute is blocked, claiming water pipe burst. Have you heard about this?"

Military Medical Research Institute. My brain raced. That was the nation's highest-level virus research center, specializing in dangerous pathogens. If something went wrong there... "Lin Xue, listen to me," I lowered my voice. "Don't go home. Go to our newly renovated house. Buy enough water and food on the way - as much as you can."

"What? What's wrong?" She immediately became alert. "I can't say too much on the phone. I'll come find you as soon as I finish here. Pack everything. We might need to stay there for a while."

After hanging up, I began searching for more information on the computer. If there really was a dangerous virus leak, there would be more signs. I hacked into the traffic management system and found that all road surveillance within a 3-kilometer radius of the Military Medical Research Institute was offline, and police deployment in surrounding areas had increased fivefold compared to normal.

Meanwhile, citywide emergency drug allocation records showed that antiviral medications and broad-spectrum antibiotics usage had skyrocketed by 400% in the past 48 hours. The final piece of evidence came from the government's internal system. Using a vulnerability I had discovered but never used before, I saw a file marked "Top Secret" with highly encrypted content, but the title was clearly visible: "AN-37 Virus Emergency Response Plan."

My blood nearly froze. AN-37 was a code name well-known in certain circles - a military-developed neurological targeting virus that could rapidly affect brain function, causing cognitive abilities and social behavior to collapse. The entire puzzle was finally complete: the Military Research Institute had leaked the AN-37 virus, the first batch of infected were sent to hospitals, but soon medical staff were also infected. Authorities were trying to control the situation, but from the data, things were spiraling out of control.

I immediately cleared access traces, closed all windows, and called Lin Xue. "Buy the most water and food you can, and medicine, especially antibiotics and fever reducers." My voice was calm, but my hands were shaking. "We probably have 36 hours to prepare before everything goes out of control."

"What exactly did you discover?" Lin Xue's tone became serious. "Hospital data shows a dangerous disease is spreading. The government is trying to control it, but they're starting to fail. We need to prepare for the worst."

After hanging up, I packed the items on my desk, pretending nothing was wrong as I walked out of the monitoring room. In my office, I quickly copied several important files to a USB drive, then deleted all traces. "Director Li, my wife's flight was moved up. I need to pick her up now." I knocked and informed her.

"So urgent?" She frowned but quickly nodded. "Alright, call if there's anything urgent." Walking out of the hospital entrance, the sun was bright and pedestrians hurried by. On the surface, the city remained peaceful, but I knew a storm was brewing, and we only had 36 hours to prepare.

I drove away from the hospital parking lot, my mind already calculating every detail of our survival plan. If my guess was correct, this world would completely change within three days.
