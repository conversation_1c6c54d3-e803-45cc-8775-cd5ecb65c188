#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速运行脚本 - 中文故事转英文视频
简化版本，一键运行完整流程
"""

import os
import sys
from pathlib import Path

def main():
    print("🎬 中文故事转英文朗读视频 - 一键流水线")
    print("=" * 50)
    
    # 检查输入文件
    story_file = "story.txt"
    if not os.path.exists(story_file):
        print(f"❌ 错误：找不到输入文件 '{story_file}'")
        print("请确保当前目录下有 story.txt 文件")
        return False
    
    # 检查必要的脚本文件
    required_files = [
        "translate_story_optimized.py",
        "sentence_splitter.py", 
        "video_production_script.py",
        "story_to_video_pipeline.py"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"❌ 错误：缺少必要文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必要文件检查通过")
    
    # 导入并运行流水线
    try:
        from story_to_video_pipeline import StoryToVideoPipeline
        
        # 创建配置
        config = {
            'project_name': 'story_video',
            'output_dir': 'output'
        }
        
        # 运行流水线
        pipeline = StoryToVideoPipeline(config)
        success = pipeline.run_pipeline(story_file)
        
        if success:
            print("\n" + "=" * 50)
            print("🎉 流水线执行成功！")
            print(f"📁 输出目录: {pipeline.output_path}")
            print("\n📋 生成的关键文件:")
            
            key_files = [
                ('朗读脚本', 'reading_script.json'),
                ('视频脚本', 'video_production_script.json'),
                ('制作指导', 'video_production_guide.md'),
                ('项目报告', 'project_report.md')
            ]
            
            for name, filename in key_files:
                filepath = pipeline.output_path / filename
                if filepath.exists():
                    print(f"   ✅ {name}: {filename}")
                else:
                    print(f"   ❌ {name}: {filename} (未生成)")
            
            print("\n🚀 下一步:")
            print("1. 使用朗读脚本进行音频录制")
            print("2. 参考视频脚本进行视频制作")
            print("3. 查看制作指导获取详细说明")
            
            return True
        else:
            print("\n💥 流水线执行失败，请检查错误信息")
            return False
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有脚本文件都在当前目录下")
        return False
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
