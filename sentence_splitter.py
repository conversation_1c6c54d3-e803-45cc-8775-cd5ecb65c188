#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
English Story Sentence Splitter for Audio Reading
"""

import json
import re
from typing import List, Dict

class SentenceSplitter:
    def __init__(self):
        self.max_sentence_length = 120  # 适合朗读的最大句子长度
        self.min_sentence_length = 20   # 最小句子长度
    
    def split_into_reading_sentences(self, text: str) -> List[Dict]:
        """
        将英文文本拆分成适合朗读的句子，每个句子包含元数据
        """
        # 首先按段落分割
        paragraphs = text.split('\n\n')
        
        all_sentences = []
        sentence_id = 1
        
        for para_idx, paragraph in enumerate(paragraphs):
            if not paragraph.strip():
                continue
                
            # 按句号、感叹号、问号分割
            raw_sentences = re.split(r'[.!?]+', paragraph)
            
            for sentence in raw_sentences:
                sentence = sentence.strip()
                if not sentence:
                    continue
                
                # 如果句子太长，在逗号或分号处进一步分割
                if len(sentence) > self.max_sentence_length:
                    sub_sentences = self.split_long_sentence(sentence)
                    for sub_sentence in sub_sentences:
                        if len(sub_sentence.strip()) >= self.min_sentence_length:
                            all_sentences.append({
                                "id": sentence_id,
                                "text": sub_sentence.strip() + ".",
                                "paragraph": para_idx + 1,
                                "length": len(sub_sentence.strip()),
                                "estimated_duration": self.estimate_reading_time(sub_sentence.strip()),
                                "type": "narrative"
                            })
                            sentence_id += 1
                else:
                    if len(sentence) >= self.min_sentence_length:
                        all_sentences.append({
                            "id": sentence_id,
                            "text": sentence + ".",
                            "paragraph": para_idx + 1,
                            "length": len(sentence),
                            "estimated_duration": self.estimate_reading_time(sentence),
                            "type": self.classify_sentence_type(sentence)
                        })
                        sentence_id += 1
        
        return all_sentences
    
    def split_long_sentence(self, sentence: str) -> List[str]:
        """
        将过长的句子在合适的位置分割
        """
        # 在逗号、分号、连词处分割
        split_points = [', ', '; ', ' and ', ' but ', ' or ', ' so ', ' yet ', ' for ']
        
        parts = [sentence]
        
        for split_point in split_points:
            new_parts = []
            for part in parts:
                if len(part) > self.max_sentence_length:
                    sub_parts = part.split(split_point)
                    current_part = ""
                    for i, sub_part in enumerate(sub_parts):
                        if len(current_part + sub_part) < self.max_sentence_length:
                            current_part += sub_part
                            if i < len(sub_parts) - 1:
                                current_part += split_point
                        else:
                            if current_part:
                                new_parts.append(current_part.strip())
                            current_part = sub_part
                            if i < len(sub_parts) - 1:
                                current_part += split_point
                    if current_part:
                        new_parts.append(current_part.strip())
                else:
                    new_parts.append(part)
            parts = new_parts
        
        return parts
    
    def estimate_reading_time(self, text: str) -> float:
        """
        估算朗读时间（秒）
        假设平均朗读速度为每分钟150个单词
        """
        word_count = len(text.split())
        reading_speed_wpm = 150  # words per minute
        return (word_count / reading_speed_wpm) * 60
    
    def classify_sentence_type(self, sentence: str) -> str:
        """
        分类句子类型，用于后续的语音合成参数调整
        """
        sentence_lower = sentence.lower()
        
        if any(word in sentence_lower for word in ['said', 'asked', 'replied', 'shouted', 'whispered']):
            return "dialogue"
        elif sentence.endswith('?'):
            return "question"
        elif sentence.endswith('!'):
            return "exclamation"
        elif any(word in sentence_lower for word in ['suddenly', 'immediately', 'quickly', 'rapidly']):
            return "action"
        elif any(word in sentence_lower for word in ['thought', 'realized', 'understood', 'remembered']):
            return "internal"
        else:
            return "narrative"
    
    def create_reading_script(self, sentences: List[Dict]) -> Dict:
        """
        创建完整的朗读脚本，包含时间安排和元数据
        """
        total_duration = sum(s['estimated_duration'] for s in sentences)
        
        # 添加累积时间
        cumulative_time = 0
        for sentence in sentences:
            sentence['start_time'] = cumulative_time
            sentence['end_time'] = cumulative_time + sentence['estimated_duration']
            cumulative_time += sentence['estimated_duration'] + 0.5  # 0.5秒停顿
        
        script = {
            "metadata": {
                "title": "Hospital Data Crisis - A Survival Story",
                "total_sentences": len(sentences),
                "estimated_total_duration": total_duration + (len(sentences) * 0.5),  # 包含停顿时间
                "average_sentence_length": sum(s['length'] for s in sentences) / len(sentences),
                "language": "English",
                "genre": "Thriller/Survival"
            },
            "sentences": sentences,
            "reading_instructions": {
                "narrative": "Normal pace, clear pronunciation",
                "dialogue": "Slightly faster, more emotional",
                "question": "Rising intonation at the end",
                "exclamation": "Emphasis and energy",
                "action": "Faster pace, building tension",
                "internal": "Slower, more thoughtful pace"
            }
        }
        
        return script
    
    def process_story_file(self, input_file: str, output_file: str):
        """
        处理故事文件并生成朗读脚本
        """
        print(f"正在读取故事文件: {input_file}")
        with open(input_file, 'r', encoding='utf-8') as f:
            story_text = f.read()
        
        print("正在分析和拆分句子...")
        sentences = self.split_into_reading_sentences(story_text)
        
        print("正在创建朗读脚本...")
        script = self.create_reading_script(sentences)
        
        print(f"正在保存到: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(script, f, ensure_ascii=False, indent=2)
        
        print(f"\n=== 处理完成 ===")
        print(f"总句子数: {script['metadata']['total_sentences']}")
        print(f"预估总时长: {script['metadata']['estimated_total_duration']:.1f} 秒 ({script['metadata']['estimated_total_duration']/60:.1f} 分钟)")
        print(f"平均句子长度: {script['metadata']['average_sentence_length']:.1f} 字符")
        
        # 显示前10个句子作为预览
        print(f"\n=== 前10个朗读句子预览 ===")
        for i, sentence in enumerate(script['sentences'][:10]):
            print(f"{sentence['id']:2d}. [{sentence['type']:9s}] {sentence['text']}")
            print(f"    时长: {sentence['estimated_duration']:.1f}s | 开始: {sentence['start_time']:.1f}s")
        
        if len(script['sentences']) > 10:
            print(f"... 还有 {len(script['sentences']) - 10} 个句子")
        
        return script

if __name__ == "__main__":
    splitter = SentenceSplitter()
    script = splitter.process_story_file("english_story_complete.txt", "reading_script.json")
